"use client"

import type React from "react"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Database, Plus, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface CreateDatabaseDialogProps {
  children: React.ReactNode
}

export function CreateDatabaseDialog({ children }: CreateDatabaseDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    type: "",
    description: "",
    maxConnections: "100",
    charset: "utf8mb4",
  })
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    toast({
      title: "Database Created",
      description: `Database "${formData.name}" has been created successfully.`,
    })

    setLoading(false)
    setOpen(false)
    setFormData({
      name: "",
      type: "",
      description: "",
      maxConnections: "100",
      charset: "utf8mb4",
    })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="glass-ultra border border-border rounded-2xl shadow-glass max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center shadow-lg">
              <Database className="w-6 h-6 text-white" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold text-foreground">Create Database</DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Set up a new database instance for your applications.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-foreground font-medium">
              Database Name
            </Label>
            <Input
              id="name"
              placeholder="my_database"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="glass-input"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type" className="text-foreground font-medium">
              Database Type
            </Label>
            <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value })}>
              <SelectTrigger className="glass-input">
                <SelectValue placeholder="Select database type" />
              </SelectTrigger>
              <SelectContent className="glass-ultra border-border">
                <SelectItem value="mysql">MySQL 8.0</SelectItem>
                <SelectItem value="postgresql">PostgreSQL 15</SelectItem>
                <SelectItem value="redis">Redis 7.0</SelectItem>
                <SelectItem value="mongodb">MongoDB 6.0</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxConnections" className="text-foreground font-medium">
                Max Connections
              </Label>
              <Input
                id="maxConnections"
                type="number"
                placeholder="100"
                value={formData.maxConnections}
                onChange={(e) => setFormData({ ...formData, maxConnections: e.target.value })}
                className="glass-input"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="charset" className="text-foreground font-medium">
                Charset
              </Label>
              <Select value={formData.charset} onValueChange={(value) => setFormData({ ...formData, charset: value })}>
                <SelectTrigger className="glass-input">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass-ultra border-border">
                  <SelectItem value="utf8mb4">utf8mb4</SelectItem>
                  <SelectItem value="utf8">utf8</SelectItem>
                  <SelectItem value="latin1">latin1</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-foreground font-medium">
              Description (Optional)
            </Label>
            <Textarea
              id="description"
              placeholder="Database description..."
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="glass-input resize-none"
              rows={3}
            />
          </div>

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="secondary-button bg-transparent"
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.name || !formData.type} className="premium-button">
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Database
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
