/**
 * Pterodactyl Panel User Management Hooks
 *
 * This module provides comprehensive TypeScript hooks for managing users
 * through the Pterodactyl Panel Application API.
 *
 * Features:
 * - Complete CRUD operations for users
 * - Type-safe API interactions
 * - Comprehensive error handling
 * - Support for filtering, pagination, and relationships
 * - Environment variable integration
 */

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

/**
 * Base Pterodactyl API response structure
 */
export interface PterodactylResponse<T> {
  object: string;
  attributes?: T;
  relationships?: Record<string, any>;
}

/**
 * Paginated list response structure
 */
export interface PterodactylListResponse<T> {
  object: "list";
  data: PterodactylResponse<T>[];
  meta: {
    pagination: {
      total: number;
      count: number;
      per_page: number;
      current_page: number;
      total_pages: number;
      links: Record<string, any>;
    };
  };
}

/**
 * User attributes from Pterodactyl API
 */
export interface PterodactylUser {
  id: number;
  external_id: string | null;
  uuid: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  language: string;
  root_admin: boolean;
  "2fa": boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Server attributes for user relationships
 */
export interface PterodactylServer {
  id: number;
  external_id: string | null;
  uuid: string;
  identifier: string;
  name: string;
  description: string;
  status: string | null;
  suspended: boolean;
  limits: {
    memory: number;
    swap: number;
    disk: number;
    io: number;
    cpu: number;
    threads: string | null;
    oom_disabled: boolean;
  };
  feature_limits: {
    databases: number;
    allocations: number;
    backups: number;
  };
  user: number;
  node: number;
  allocation: number;
  nest: number;
  egg: number;
  container: {
    startup_command: string;
    image: string;
    installed: boolean;
    environment: Record<string, any>;
  };
  created_at: string;
  updated_at: string;
}

/**
 * User creation request data
 */
export interface UserCreateData {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  password?: string;
  language?: string;
  root_admin?: boolean;
  external_id?: string;
}

/**
 * User update request data
 */
export interface UserUpdateData {
  email?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  password?: string;
  language?: string;
  root_admin?: boolean;
  external_id?: string;
}

/**
 * Query parameters for listing users
 */
export interface UserListParams {
  page?: number;
  per_page?: number;
  "filter[email]"?: string;
  "filter[uuid]"?: string;
  "filter[username]"?: string;
  "filter[external_id]"?: string;
  sort?: "id" | "uuid" | "username" | "email" | "created_at" | "updated_at";
  include?: "servers";
}

/**
 * Query parameters for getting user details
 */
export interface UserDetailsParams {
  include?: "servers";
}

/**
 * Custom error class for Pterodactyl API errors
 */
export class PterodactylError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = "PterodactylError";
  }
}

// ============================================================================
// API CLIENT UTILITIES
// ============================================================================

/**
 * Get environment configuration for Pterodactyl API
 */
function getPterodactylConfig() {
  const panelUrl = process.env.PANEL_URL || process.env.NEXT_PUBLIC_PANEL_URL;
  const apiKey = process.env.PANEL_API_KEY || process.env.NEXT_PUBLIC_PANEL_API_KEY;

  if (!panelUrl || !apiKey) {
    throw new PterodactylError(
      "Missing Pterodactyl configuration. Please set PANEL_URL and PANEL_API_KEY environment variables.",
      500,
      "MISSING_CONFIG"
    );
  }

  return {
    baseUrl: panelUrl.endsWith("/") ? panelUrl.slice(0, -1) : panelUrl,
    apiKey: apiKey,
  };
}

/**
 * Get default headers for Pterodactyl API requests
 */
function getDefaultHeaders(includeContentType = false): HeadersInit {
  const { apiKey } = getPterodactylConfig();

  const headers: HeadersInit = {
    "Authorization": `Bearer ${apiKey}`,
    "Accept": "Application/vnd.pterodactyl.v1+json",
  };

  if (includeContentType) {
    headers["Content-Type"] = "application/json";
  }

  return headers;
}

/**
 * Handle API response and errors
 */
async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    let errorCode = response.status.toString();
    let errorDetails: any = null;

    try {
      const errorData = await response.json();
      if (errorData.errors && Array.isArray(errorData.errors)) {
        errorMessage = errorData.errors.map((err: any) => err.detail || err.message).join(", ");
        errorCode = errorData.errors[0]?.code || errorCode;
        errorDetails = errorData.errors;
      }
    } catch {
      // If we can't parse the error response, use the default message
    }

    throw new PterodactylError(errorMessage, response.status, errorCode, errorDetails);
  }

  try {
    return await response.json();
  } catch (error) {
    throw new PterodactylError(
      "Failed to parse API response",
      500,
      "PARSE_ERROR",
      error
    );
  }
}

/**
 * Build URL with query parameters
 */
function buildUrl(baseUrl: string, endpoint: string, params?: Record<string, any>): string {
  const url = new URL(`${baseUrl}${endpoint}`);

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, value.toString());
      }
    });
  }

  return url.toString();
}

// ============================================================================
// USER MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Create a new user in the Pterodactyl panel
 *
 * @param userData - User creation data
 * @returns Promise<PterodactylResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * const newUser = await panelUserCreate({
 *   email: "<EMAIL>",
 *   username: "newuser",
 *   first_name: "John",
 *   last_name: "Doe",
 *   password: "secure_password_123",
 *   language: "en",
 *   root_admin: false
 * });
 * ```
 */
export async function panelUserCreate(
  userData: UserCreateData
): Promise<PterodactylResponse<PterodactylUser>> {
  const { baseUrl } = getPterodactylConfig();

  const response = await fetch(`${baseUrl}/api/application/users`, {
    method: "POST",
    headers: getDefaultHeaders(true),
    body: JSON.stringify(userData),
  });

  return handleApiResponse<PterodactylResponse<PterodactylUser>>(response);
}

/**
 * Update an existing user in the Pterodactyl panel
 *
 * @param userId - The ID of the user to update
 * @param updateData - User update data
 * @returns Promise<PterodactylResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * const updatedUser = await panelUserUpdate(1, {
 *   first_name: "Jane",
 *   last_name: "Smith",
 *   language: "fr"
 * });
 * ```
 */
export async function panelUserUpdate(
  userId: number,
  updateData: UserUpdateData
): Promise<PterodactylResponse<PterodactylUser>> {
  const { baseUrl } = getPterodactylConfig();

  const response = await fetch(`${baseUrl}/api/application/users/${userId}`, {
    method: "PATCH",
    headers: getDefaultHeaders(true),
    body: JSON.stringify(updateData),
  });

  return handleApiResponse<PterodactylResponse<PterodactylUser>>(response);
}

/**
 * Get details of a specific user from the Pterodactyl panel
 *
 * @param userId - The ID of the user to retrieve
 * @param params - Optional query parameters
 * @returns Promise<PterodactylResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * // Get user without relationships
 * const user = await panelUserGetDetails(1);
 *
 * // Get user with servers
 * const userWithServers = await panelUserGetDetails(1, { include: "servers" });
 * ```
 */
export async function panelUserGetDetails(
  userId: number,
  params?: UserDetailsParams
): Promise<PterodactylResponse<PterodactylUser>> {
  const { baseUrl } = getPterodactylConfig();
  const url = buildUrl(baseUrl, `/api/application/users/${userId}`, params);

  const response = await fetch(url, {
    method: "GET",
    headers: getDefaultHeaders(),
  });

  return handleApiResponse<PterodactylResponse<PterodactylUser>>(response);
}

/**
 * Get all users from the Pterodactyl panel with optional filtering and pagination
 *
 * @param params - Optional query parameters for filtering, pagination, and includes
 * @returns Promise<PterodactylListResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * // Get all users with default pagination
 * const users = await panelUserGetAll();
 *
 * // Get users with filtering and custom pagination
 * const filteredUsers = await panelUserGetAll({
 *   per_page: 25,
 *   page: 1,
 *   "filter[email]": "<EMAIL>",
 *   include: "servers",
 *   sort: "created_at"
 * });
 * ```
 */
export async function panelUserGetAll(
  params?: UserListParams
): Promise<PterodactylListResponse<PterodactylUser>> {
  const { baseUrl } = getPterodactylConfig();
  const url = buildUrl(baseUrl, "/api/application/users", params);

  const response = await fetch(url, {
    method: "GET",
    headers: getDefaultHeaders(),
  });

  return handleApiResponse<PterodactylListResponse<PterodactylUser>>(response);
}

/**
 * Get servers associated with a specific user
 *
 * @param userId - The ID of the user
 * @returns Promise<PterodactylResponse<PterodactylUser> with servers relationship>
 *
 * @example
 * ```typescript
 * const userServers = await panelUserGetServers(1);
 * const servers = userServers.relationships?.servers?.data || [];
 * ```
 */
export async function panelUserGetServers(
  userId: number
): Promise<PterodactylResponse<PterodactylUser>> {
  return panelUserGetDetails(userId, { include: "servers" });
}

/**
 * Delete a user from the Pterodactyl panel
 *
 * @param userId - The ID of the user to delete
 * @returns Promise<void>
 *
 * @example
 * ```typescript
 * await panelUserDelete(1);
 * console.log("User deleted successfully");
 * ```
 */
export async function panelUserDelete(userId: number): Promise<void> {
  const { baseUrl } = getPterodactylConfig();

  const response = await fetch(`${baseUrl}/api/application/users/${userId}`, {
    method: "DELETE",
    headers: getDefaultHeaders(),
  });

  if (response.status !== 204) {
    await handleApiResponse(response);
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Search for users by email address
 *
 * @param email - Email address to search for
 * @param exactMatch - Whether to perform exact match or partial match
 * @returns Promise<PterodactylListResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * const users = await panelUserSearchByEmail("<EMAIL>", true);
 * ```
 */
export async function panelUserSearchByEmail(
  email: string,
  exactMatch = true
): Promise<PterodactylListResponse<PterodactylUser>> {
  return panelUserGetAll({
    "filter[email]": exactMatch ? email : `*${email}*`,
    per_page: 100,
  });
}

/**
 * Search for users by username
 *
 * @param username - Username to search for
 * @param exactMatch - Whether to perform exact match or partial match
 * @returns Promise<PterodactylListResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * const users = await panelUserSearchByUsername("admin", false);
 * ```
 */
export async function panelUserSearchByUsername(
  username: string,
  exactMatch = true
): Promise<PterodactylListResponse<PterodactylUser>> {
  return panelUserGetAll({
    "filter[username]": exactMatch ? username : `*${username}*`,
    per_page: 100,
  });
}

/**
 * Get user by UUID
 *
 * @param uuid - User UUID
 * @returns Promise<PterodactylListResponse<PterodactylUser>>
 *
 * @example
 * ```typescript
 * const users = await panelUserGetByUuid("c4022c6c-9bf1-4a23-bff9-519cceb38335");
 * const user = users.data[0]; // Should be exactly one user
 * ```
 */
export async function panelUserGetByUuid(
  uuid: string
): Promise<PterodactylListResponse<PterodactylUser>> {
  return panelUserGetAll({
    "filter[uuid]": uuid,
    per_page: 1,
  });
}

/**
 * Get all admin users
 *
 * @param includeServers - Whether to include server relationships
 * @returns Promise<PterodactylUser[]>
 *
 * @example
 * ```typescript
 * const adminUsers = await panelUserGetAdmins(true);
 * ```
 */
export async function panelUserGetAdmins(
  includeServers = false
): Promise<PterodactylUser[]> {
  const response = await panelUserGetAll({
    per_page: 100,
    include: includeServers ? "servers" : undefined,
  });

  return response.data
    .filter(user => user.attributes?.root_admin === true)
    .map(user => user.attributes!)
    .filter(Boolean);
}

/**
 * Check if a user exists by email
 *
 * @param email - Email address to check
 * @returns Promise<boolean>
 *
 * @example
 * ```typescript
 * const exists = await panelUserExistsByEmail("<EMAIL>");
 * if (!exists) {
 *   // Create the user
 * }
 * ```
 */
export async function panelUserExistsByEmail(email: string): Promise<boolean> {
  try {
    const response = await panelUserSearchByEmail(email, true);
    return response.data.length > 0;
  } catch (error) {
    if (error instanceof PterodactylError && error.status === 404) {
      return false;
    }
    throw error;
  }
}

/**
 * Check if a user exists by username
 *
 * @param username - Username to check
 * @returns Promise<boolean>
 *
 * @example
 * ```typescript
 * const exists = await panelUserExistsByUsername("admin");
 * ```
 */
export async function panelUserExistsByUsername(username: string): Promise<boolean> {
  try {
    const response = await panelUserSearchByUsername(username, true);
    return response.data.length > 0;
  } catch (error) {
    if (error instanceof PterodactylError && error.status === 404) {
      return false;
    }
    throw error;
  }
}

/**
 * Get user statistics
 *
 * @returns Promise<UserStats>
 *
 * @example
 * ```typescript
 * const stats = await panelUserGetStats();
 * console.log(`Total users: ${stats.total}, Admins: ${stats.admins}`);
 * ```
 */
export interface UserStats {
  total: number;
  admins: number;
  regular: number;
  with2fa: number;
  without2fa: number;
}

export async function panelUserGetStats(): Promise<UserStats> {
  const response = await panelUserGetAll({ per_page: 100 });

  const users = response.data.map(user => user.attributes!).filter(Boolean);

  const stats: UserStats = {
    total: response.meta.pagination.total,
    admins: users.filter(user => user.root_admin).length,
    regular: users.filter(user => !user.root_admin).length,
    with2fa: users.filter(user => user["2fa"]).length,
    without2fa: users.filter(user => !user["2fa"]).length,
  };

  return stats;
}

// ============================================================================
// BATCH OPERATIONS
// ============================================================================

/**
 * Create multiple users in batch
 *
 * @param usersData - Array of user creation data
 * @param options - Batch operation options
 * @returns Promise<BatchResult<PterodactylUser>>
 *
 * @example
 * ```typescript
 * const result = await panelUserCreateBatch([
 *   { email: "<EMAIL>", username: "user1", first_name: "User", last_name: "One" },
 *   { email: "<EMAIL>", username: "user2", first_name: "User", last_name: "Two" }
 * ]);
 * ```
 */
export interface BatchResult<T> {
  successful: T[];
  failed: Array<{ data: any; error: PterodactylError }>;
  total: number;
  successCount: number;
  failureCount: number;
}

export interface BatchOptions {
  continueOnError?: boolean;
  delayBetweenRequests?: number;
}

export async function panelUserCreateBatch(
  usersData: UserCreateData[],
  options: BatchOptions = { continueOnError: true, delayBetweenRequests: 100 }
): Promise<BatchResult<PterodactylUser>> {
  const result: BatchResult<PterodactylUser> = {
    successful: [],
    failed: [],
    total: usersData.length,
    successCount: 0,
    failureCount: 0,
  };

  for (const userData of usersData) {
    try {
      const response = await panelUserCreate(userData);
      if (response.attributes) {
        result.successful.push(response.attributes);
        result.successCount++;
      }

      // Add delay between requests to avoid rate limiting
      if (options.delayBetweenRequests && options.delayBetweenRequests > 0) {
        await new Promise(resolve => setTimeout(resolve, options.delayBetweenRequests));
      }
    } catch (error) {
      const pterodactylError = error instanceof PterodactylError
        ? error
        : new PterodactylError("Unknown error", 500, "UNKNOWN", error);

      result.failed.push({ data: userData, error: pterodactylError });
      result.failureCount++;

      if (!options.continueOnError) {
        break;
      }
    }
  }

  return result;
}

// ============================================================================
// EXPORTS SUMMARY
// ============================================================================

/**
 * All exported functions for Pterodactyl user management:
 *
 * Core CRUD Operations:
 * - panelUserCreate(userData) - Create a new user
 * - panelUserUpdate(userId, updateData) - Update existing user
 * - panelUserGetDetails(userId, params?) - Get single user details
 * - panelUserGetAll(params?) - Get all users with filtering/pagination
 * - panelUserGetServers(userId) - Get servers for a specific user
 * - panelUserDelete(userId) - Delete a user
 *
 * Search & Utility Functions:
 * - panelUserSearchByEmail(email, exactMatch?) - Search users by email
 * - panelUserSearchByUsername(username, exactMatch?) - Search users by username
 * - panelUserGetByUuid(uuid) - Get user by UUID
 * - panelUserGetAdmins(includeServers?) - Get all admin users
 * - panelUserExistsByEmail(email) - Check if user exists by email
 * - panelUserExistsByUsername(username) - Check if user exists by username
 * - panelUserGetStats() - Get user statistics
 *
 * Batch Operations:
 * - panelUserCreateBatch(usersData, options?) - Create multiple users
 *
 * Types & Interfaces:
 * - PterodactylUser - User data structure
 * - UserCreateData - User creation request data
 * - UserUpdateData - User update request data
 * - UserListParams - Query parameters for listing users
 * - UserDetailsParams - Query parameters for user details
 * - PterodactylError - Custom error class
 * - BatchResult<T> - Batch operation result
 * - UserStats - User statistics structure
 */

// ============================================================================
// USAGE EXAMPLES
// ============================================================================

/**
 * Complete usage examples:
 *
 * @example Basic user creation
 * ```typescript
 * import { panelUserCreate } from '@/hooks/managers/pterodactyl/users';
 *
 * try {
 *   const newUser = await panelUserCreate({
 *     email: "<EMAIL>",
 *     username: "johndoe",
 *     first_name: "John",
 *     last_name: "Doe",
 *     password: "secure_password_123",
 *     language: "en",
 *     root_admin: false
 *   });
 *
 *   console.log("User created:", newUser.attributes);
 * } catch (error) {
 *   if (error instanceof PterodactylError) {
 *     console.error("API Error:", error.message, error.status);
 *   }
 * }
 * ```
 *
 * @example User management with error handling
 * ```typescript
 * import {
 *   panelUserGetAll,
 *   panelUserUpdate,
 *   panelUserDelete,
 *   PterodactylError
 * } from '@/hooks/managers/pterodactyl/users';
 *
 * async function manageUsers() {
 *   try {
 *     // Get all users with servers
 *     const users = await panelUserGetAll({
 *       include: "servers",
 *       per_page: 50,
 *       sort: "created_at"
 *     });
 *
 *     for (const userResponse of users.data) {
 *       const user = userResponse.attributes;
 *       if (!user) continue;
 *
 *       // Update user language if needed
 *       if (user.language !== "en") {
 *         await panelUserUpdate(user.id, { language: "en" });
 *       }
 *
 *       // Delete inactive users (example logic)
 *       const servers = userResponse.relationships?.servers?.data || [];
 *       if (servers.length === 0 && !user.root_admin) {
 *         await panelUserDelete(user.id);
 *       }
 *     }
 *   } catch (error) {
 *     if (error instanceof PterodactylError) {
 *       console.error(`Pterodactyl API Error: ${error.message} (${error.status})`);
 *     } else {
 *       console.error("Unexpected error:", error);
 *     }
 *   }
 * }
 * ```
 *
 * @example Batch user creation
 * ```typescript
 * import { panelUserCreateBatch } from '@/hooks/managers/pterodactyl/users';
 *
 * const newUsers = [
 *   { email: "<EMAIL>", username: "user1", first_name: "User", last_name: "One" },
 *   { email: "<EMAIL>", username: "user2", first_name: "User", last_name: "Two" },
 *   { email: "<EMAIL>", username: "user3", first_name: "User", last_name: "Three" }
 * ];
 *
 * const result = await panelUserCreateBatch(newUsers, {
 *   continueOnError: true,
 *   delayBetweenRequests: 200
 * });
 *
 * console.log(`Created ${result.successCount}/${result.total} users`);
 * if (result.failed.length > 0) {
 *   console.log("Failed users:", result.failed);
 * }
 * ```
 *
 * @example User search and validation
 * ```typescript
 * import {
 *   panelUserExistsByEmail,
 *   panelUserSearchByUsername,
 *   panelUserGetStats
 * } from '@/hooks/managers/pterodactyl/users';
 *
 * // Check if user exists before creating
 * const emailExists = await panelUserExistsByEmail("<EMAIL>");
 * if (emailExists) {
 *   console.log("User already exists");
 * }
 *
 * // Search for users by partial username
 * const adminUsers = await panelUserSearchByUsername("admin", false);
 * console.log("Found admin users:", adminUsers.data.length);
 *
 * // Get user statistics
 * const stats = await panelUserGetStats();
 * console.log(`Total: ${stats.total}, Admins: ${stats.admins}, 2FA: ${stats.with2fa}`);
 * ```
 */