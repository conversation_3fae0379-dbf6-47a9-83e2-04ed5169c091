"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { User, Server, Setting<PERSON>, Shield } from "lucide-react"

export default function ActivityFeed() {
  const activities = [
    {
      icon: User,
      title: "Player joined",
      description: "<PERSON> joined Minecraft Survival",
      time: "2 min ago",
      color: "from-emerald-500 to-green-500",
    },
    {
      icon: Server,
      title: "Server started",
      description: "Creative Build Server online",
      time: "15 min ago",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Settings,
      title: "Settings updated",
      description: "Max players changed to 50",
      time: "1h ago",
      color: "from-purple-500 to-pink-500",
    },
    {
      icon: Shield,
      title: "Security alert",
      description: "Failed login attempt",
      time: "2h ago",
      color: "from-orange-500 to-red-500",
    },
  ]

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader className="pb-3">
        <CardTitle className="text-foreground text-lg">Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activities.map((activity, index) => (
            <div key={index} className="flex items-start space-x-3">
              <div
                className={`w-8 h-8 rounded-lg bg-gradient-to-br ${activity.color} flex items-center justify-center flex-shrink-0 shadow-lg`}
              >
                <activity.icon className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-foreground font-medium text-sm">{activity.title}</p>
                <p className="text-muted-foreground text-xs truncate">{activity.description}</p>
                <p className="text-muted-foreground text-xs mt-1">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
