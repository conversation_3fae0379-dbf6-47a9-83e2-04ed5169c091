"use client"

import type React from "react"

import { use<PERSON>emo } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import SystemStats from "@/components/system-stats"
import ResourceUsage from "@/components/resource-usage"
import ServerGrid from "@/components/server-grid"
import { useServerStore } from "@/stores/server-store"
import { Server, Users, Cpu, Activity, TrendingUp, Plus, Zap, HardDrive, Clock } from "lucide-react"
import Link from "next/link"

type Props = {
  onServerOpen: (id: string) => void
  serverCount?: number
}

export default function DashboardView({ onServerOpen, serverCount = 0 }: Props) {
  const servers = useServerStore((s) => s.servers)

  // Memoized calculations for better performance
  const dashboardStats = useMemo(() => {
    const onlineServers = servers.filter((s) => s.status === "online").length
    const totalPlayers = servers.reduce((n, s) => {
      const current = Number.parseInt(s.players.split("/")[0] || "0")
      return n + current
    }, 0)
    const avgCpu =
      servers.length > 0
        ? Math.round(
            servers.reduce((sum, s) => sum + Number.parseInt(s.cpu.replace("%", "") || "0"), 0) / servers.length,
          )
        : 0
    const avgMemory =
      servers.length > 0
        ? Math.round(
            servers.reduce((sum, s) => sum + Number.parseInt(s.memory.replace("%", "") || "0"), 0) / servers.length,
          )
        : 0

    return {
      onlineServers,
      totalPlayers,
      avgCpu,
      avgMemory,
      offlineServers: servers.length - onlineServers,
    }
  }, [servers])

  return (
    <div className="space-y-8">
      {/* Enhanced Overview Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <OverviewCard
          title="Total Servers"
          value={serverCount.toString()}
          change={+12}
          changeLabel="vs last month"
          color="from-blue-500 to-cyan-500"
          icon={<Server className="w-5 h-5 text-white" />}
        />
        <OverviewCard
          title="Online Now"
          value={dashboardStats.onlineServers.toString()}
          change={+8}
          changeLabel="vs yesterday"
          color="from-emerald-500 to-green-500"
          icon={<Activity className="w-5 h-5 text-white" />}
        />
        <OverviewCard
          title="Total Players"
          value={dashboardStats.totalPlayers.toString()}
          change={+25}
          changeLabel="vs last week"
          color="from-purple-500 to-pink-500"
          icon={<Users className="w-5 h-5 text-white" />}
        />
        <OverviewCard
          title="Avg CPU Usage"
          value={`${dashboardStats.avgCpu}%`}
          change={-5}
          changeLabel="vs last hour"
          color="from-orange-500 to-red-500"
          icon={<Cpu className="w-5 h-5 text-white" />}
        />
      </div>

      {/* Quick Actions */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-foreground flex items-center gap-2">
            <Zap className="w-5 h-5 text-brand" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/create-server">
              <Button className="w-full premium-button h-12">
                <Plus className="w-4 h-4 mr-2" />
                Create Server
              </Button>
            </Link>
            <Link href="/admin/databases">
              <Button variant="outline" className="w-full secondary-button bg-transparent h-12">
                <HardDrive className="w-4 h-4 mr-2" />
                Manage Databases
              </Button>
            </Link>
            <Link href="/admin/monitoring">
              <Button variant="outline" className="w-full secondary-button bg-transparent h-12">
                <Activity className="w-4 h-4 mr-2" />
                View Monitoring
              </Button>
            </Link>
            <Link href="/admin/users">
              <Button variant="outline" className="w-full secondary-button bg-transparent h-12">
                <Users className="w-4 h-4 mr-2" />
                User Management
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Main Content - Servers and System Stats */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
        {/* Servers - Takes most space */}
        <div className="xl:col-span-3 space-y-8">
          <div>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold text-foreground">Your Servers</h3>
                <p className="text-muted-foreground mt-1">
                  {dashboardStats.onlineServers} of {serverCount} servers online
                </p>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-emerald-400" />
                  <span className="text-muted-foreground">Online ({dashboardStats.onlineServers})</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-400" />
                  <span className="text-muted-foreground">Offline ({dashboardStats.offlineServers})</span>
                </div>
              </div>
            </div>
            <ServerGrid onServerSelect={(sv) => onServerOpen(sv.id)} />
          </div>

          {/* Resource Usage */}
          <div>
            <h3 className="text-xl font-bold text-foreground mb-4 flex items-center gap-2">
              <Activity className="w-5 h-5 text-brand" />
              Resource Usage
            </h3>
            <ResourceUsage />
          </div>
        </div>

        {/* System Stats - Sidebar */}
        <div className="xl:col-span-1">
          <div className="sticky top-6">
            <h3 className="text-xl font-bold text-foreground mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5 text-brand" />
              System Overview
            </h3>
            <SystemStats />
          </div>
        </div>
      </div>
    </div>
  )
}

// Enhanced OverviewCard with trends
function OverviewCard({
  title,
  value,
  change,
  changeLabel,
  color,
  icon,
}: {
  title: string
  value: string
  change?: number
  changeLabel?: string
  color: string
  icon: React.ReactNode
}) {
  return (
    <Card className="glass-ultra rounded-2xl border border-border shadow-glass hover:shadow-glass-lg transition-all duration-300 group">
      <CardContent className="p-4 md:p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="min-w-0 flex-1">
            <p className="text-muted-foreground text-sm font-medium truncate">{title}</p>
            <p className="text-2xl md:text-3xl font-black text-foreground mt-1">{value}</p>
          </div>
          <div
            className={`w-10 h-10 md:w-12 md:h-12 rounded-xl bg-gradient-to-br ${color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0`}
          >
            {icon}
          </div>
        </div>

        {change !== undefined && (
          <div className="flex items-center gap-2">
            <div className={`flex items-center gap-1 ${change >= 0 ? "text-emerald-400" : "text-red-400"}`}>
              <TrendingUp className={`w-3 h-3 ${change < 0 ? "rotate-180" : ""}`} />
              <span className="text-xs font-medium">
                {change >= 0 ? "+" : ""}
                {change}%
              </span>
            </div>
            {changeLabel && <span className="text-xs text-muted-foreground">{changeLabel}</span>}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
