@import "tailwindcss";
@import "tw-animate-css";

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.75rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* Enhanced Typography */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
    text-rendering: optimizeLegibility;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }
  h2 {
    @apply text-3xl lg:text-4xl;
  }
  h3 {
    @apply text-2xl lg:text-3xl;
  }
  h4 {
    @apply text-xl lg:text-2xl;
  }
  h5 {
    @apply text-lg lg:text-xl;
  }
  h6 {
    @apply text-base lg:text-lg;
  }

  /* Enhanced text shadows for depth */
  .brand-heading,
  .text-brand {
    text-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
  }

  .dark .brand-heading,
  .dark .text-brand {
    text-shadow: 0px 2px 8px rgba(0, 0, 0, 0.6);
  }

  /* Smooth transitions for all interactive elements */
  button,
  input,
  select,
  textarea,
  a {
    @apply transition-all duration-200 ease-out;
  }
}

/* Performance Optimizations for Low-End Devices */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .glass-ultra::before {
    display: none;
  }
}

/* Reduce effects on low-end devices */
@media (max-width: 768px) {
  .glass-ultra {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .shadow-glass {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .dark .shadow-glass {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  }
}

/* Ultra-Premium Glass Effects with better performance */
.glass-ultra {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.glass-ultra::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  pointer-events: none;
}

.light .glass-ultra {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.light .glass-ultra::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
}

/* Enhanced Shadow System */
.shadow-glass {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .shadow-glass {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

.shadow-glass-lg {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .shadow-glass-lg {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Premium Button System with better performance */
.premium-button {
  background: linear-gradient(135deg, var(--brand-from) 0%, var(--brand-to) 100%);
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 14px 0 color-mix(in oklab, var(--brand-from) 25%, transparent);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px 0 color-mix(in oklab, var(--brand-from) 35%, transparent);
}

.premium-button:active {
  transform: translateY(0);
}

.premium-button:disabled {
  opacity: 0.6;
  transform: none;
  cursor: not-allowed;
}

/* Enhanced Secondary Button */
.secondary-button {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px);
  color: var(--foreground);
  border: 1px solid var(--border);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.light .secondary-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--border);
}

.secondary-button:hover {
  transform: translateY(-1px);
  border-color: color-mix(in oklab, var(--border) 80%, var(--brand-from));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.light .secondary-button:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Enhanced Glass Button */
.glass-button {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(8px);
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.light .glass-button {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--border);
}

.glass-button:hover {
  border-color: color-mix(in oklab, var(--border) 70%, var(--brand-from));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.light .glass-button:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--brand-from), var(--brand-to));
  border-radius: 3px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    color-mix(in oklab, var(--brand-from) 80%, white),
    color-mix(in oklab, var(--brand-to) 80%, white)
  );
}

/* Enhanced Selection */
::selection {
  background: color-mix(in oklab, var(--brand-from) 30%, transparent);
  color: var(--foreground);
}

/* Enhanced Focus States */
*:focus-visible {
  outline: 2px solid var(--brand-from);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced Mobile Responsive */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Reduce motion for mobile performance */
  .premium-button:hover,
  .secondary-button:hover,
  .glass-button:hover {
    transform: none;
  }
}

/* Brand System */
:root {
  --brand-from: #1488cc;
  --brand-to: #2b32b2;
}

.bg-brand {
  background-image: linear-gradient(135deg, var(--brand-from), var(--brand-to));
  background-color: var(--brand-from);
}

.text-brand,
.brand-heading {
  background-image: linear-gradient(135deg, var(--brand-from), var(--brand-to));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Utility Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
