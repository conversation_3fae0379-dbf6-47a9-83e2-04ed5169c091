"use client"

import { useState } from "react"
import AdminLayout from "@/components/admin-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Database,
  Plus,
  Settings,
  Trash2,
  Search,
  MoreHorizontal,
  Activity,
  Users,
  HardDrive,
  Zap,
  Eye,
  Download,
  RefreshCw,
} from "lucide-react"
import { CreateDatabaseDialog } from "@/components/create-database-dialog"
import { DeleteConfirmationDialog } from "@/components/delete-confirmation-dialog"
import { useToast } from "@/hooks/use-toast"

export default function AdminDatabasesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; database: any | null }>({
    open: false,
    database: null,
  })
  const { toast } = useToast()

  const databases = [
    {
      id: "db1",
      name: "minecraft_survival",
      type: "MySQL 8.0",
      size: "245 MB",
      status: "online",
      connections: 12,
      maxConnections: 100,
      uptime: "15d 4h",
      queries: "1.2M",
    },
    {
      id: "db2",
      name: "rust_pvp_stats",
      type: "PostgreSQL 15",
      size: "89 MB",
      status: "online",
      connections: 5,
      maxConnections: 50,
      uptime: "8d 12h",
      queries: "456K",
    },
    {
      id: "db3",
      name: "user_sessions",
      type: "Redis 7.0",
      size: "12 MB",
      status: "offline",
      connections: 0,
      maxConnections: 1000,
      uptime: "0h",
      queries: "89K",
    },
    {
      id: "db4",
      name: "analytics_data",
      type: "MongoDB 6.0",
      size: "1.2 GB",
      status: "online",
      connections: 8,
      maxConnections: 200,
      uptime: "22d 8h",
      queries: "3.4M",
    },
  ]

  const filteredDatabases = databases.filter(
    (db) =>
      db.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      db.type.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30"
      case "offline":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "maintenance":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const getTypeIcon = (type: string) => {
    if (type.includes("MySQL")) return "🐬"
    if (type.includes("PostgreSQL")) return "🐘"
    if (type.includes("Redis")) return "🔴"
    if (type.includes("MongoDB")) return "🍃"
    return "💾"
  }

  const handleDeleteDatabase = (database: any) => {
    setDeleteDialog({ open: true, database })
  }

  const confirmDelete = () => {
    if (deleteDialog.database) {
      toast({
        title: "Database Deleted",
        description: `Database "${deleteDialog.database.name}" has been permanently deleted.`,
        variant: "destructive",
      })
    }
    setDeleteDialog({ open: false, database: null })
  }

  return (
    <AdminLayout title="Database Management" subtitle="Manage server databases and connections">
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search databases..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 glass-input"
            />
          </div>
          <CreateDatabaseDialog>
            <Button className="premium-button">
              <Plus className="w-4 h-4 mr-2" />
              Create Database
            </Button>
          </CreateDatabaseDialog>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="glass-ultra border border-border rounded-2xl">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center">
                  <Database className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{databases.length}</p>
                  <p className="text-sm text-muted-foreground">Total Databases</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-green-500 flex items-center justify-center">
                  <Activity className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {databases.filter((db) => db.status === "online").length}
                  </p>
                  <p className="text-sm text-muted-foreground">Online</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {databases.reduce((sum, db) => sum + db.connections, 0)}
                  </p>
                  <p className="text-sm text-muted-foreground">Active Connections</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-ultra border border-border rounded-2xl">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center">
                  <HardDrive className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">1.5 GB</p>
                  <p className="text-sm text-muted-foreground">Total Storage</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Database Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredDatabases.map((db) => (
            <Card
              key={db.id}
              className="glass-ultra border border-border rounded-2xl shadow-glass hover:shadow-glass-lg transition-all duration-300 group"
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center shadow-lg">
                      <span className="text-xl">{getTypeIcon(db.type)}</span>
                    </div>
                    <div>
                      <CardTitle className="text-foreground text-lg font-bold">{db.name}</CardTitle>
                      <p className="text-muted-foreground text-sm">{db.type}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(db.status)}>
                      {db.status.charAt(0).toUpperCase() + db.status.slice(1)}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="glass-ultra border-border">
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="w-4 h-4 mr-2" />
                          Configure
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="w-4 h-4 mr-2" />
                          Export
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Restart
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteDatabase(db)}
                          className="text-red-400 focus:text-red-400"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Database Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <HardDrive className="w-4 h-4 text-purple-400" />
                        <span className="text-muted-foreground">Size</span>
                      </div>
                      <span className="font-semibold text-foreground">{db.size}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-blue-400" />
                        <span className="text-muted-foreground">Connections</span>
                      </div>
                      <span className="font-semibold text-foreground">
                        {db.connections}/{db.maxConnections}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Activity className="w-4 h-4 text-emerald-400" />
                        <span className="text-muted-foreground">Uptime</span>
                      </div>
                      <span className="font-semibold text-foreground">{db.uptime}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4 text-amber-400" />
                        <span className="text-muted-foreground">Queries</span>
                      </div>
                      <span className="font-semibold text-foreground">{db.queries}</span>
                    </div>
                  </div>
                </div>

                {/* Connection Usage Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Connection Usage</span>
                    <span className="text-foreground font-medium">
                      {Math.round((db.connections / db.maxConnections) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-border rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(db.connections / db.maxConnections) * 100}%` }}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button size="sm" variant="outline" className="flex-1 secondary-button bg-transparent">
                    <Settings className="w-4 h-4 mr-2" />
                    Manage
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="secondary-button bg-transparent text-red-400 hover:text-red-300 hover:bg-red-400/10"
                    onClick={() => handleDeleteDatabase(db)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredDatabases.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-gray-500/20 to-gray-600/20 flex items-center justify-center">
              <Database className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">No databases found</h3>
            <p className="text-muted-foreground mb-6">
              {searchQuery ? "Try adjusting your search criteria." : "Create your first database to get started!"}
            </p>
            {!searchQuery && (
              <CreateDatabaseDialog>
                <Button className="premium-button">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Database
                </Button>
              </CreateDatabaseDialog>
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, database: null })}
        onConfirm={confirmDelete}
        title="Delete Database"
        description="Are you sure you want to delete this database? This action cannot be undone and all data will be permanently lost."
        itemName={deleteDialog.database?.name}
        destructive={true}
      />
    </AdminLayout>
  )
}
